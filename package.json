{"name": "debt-tracker", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "biome check .", "check:unsafe": "biome check --write --unsafe .", "check:write": "biome check --write .", "dev": "next dev --turbo", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@clerk/nextjs": "^6.23.3", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.3", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.81.5", "@trpc/client": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "currency.js": "^2.0.4", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "next": "^15.3.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "recharts": "^3.1.0", "server-only": "^0.0.1", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "zod": "^3.25.76"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/recharts": "^2.0.1", "husky": "^9.1.7", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3"}, "ct3aMetadata": {"initVersion": "7.39.3"}}